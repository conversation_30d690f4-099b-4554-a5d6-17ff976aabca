package com.laoshu198838.export;

import com.aspose.cells.Workbook;
import com.aspose.cells.Worksheet;

public class CheckWorksheets {
    public static void main(String[] args) {
        try {
            String templatePath = "api-gateway/src/main/resources/templates/逾期债权清收统计表（模版）.xlsx";
            Workbook workbook = new Workbook(templatePath);
            
            System.out.println("工作表数量: " + workbook.getWorksheets().getCount());
            for (int i = 0; i < workbook.getWorksheets().getCount(); i++) {
                Worksheet sheet = workbook.getWorksheets().get(i);
                System.out.println("工作表 " + i + ": '" + sheet.getName() + "' (最大行: " + sheet.getCells().getMaxDataRow() + ")");
            }
            
            // 尝试获取表6
            Worksheet sheet6 = workbook.getWorksheets().get("表6-风险准备金统计表");
            if (sheet6 != null) {
                System.out.println("\n找到表6-风险准备金统计表");
                System.out.println("最大行数: " + sheet6.getCells().getMaxDataRow());
                
                // 打印前150行B列内容
                System.out.println("\nB列内容 (前150行):");
                for (int row = 0; row <= 150 && row <= sheet6.getCells().getMaxDataRow(); row++) {
                    String value = sheet6.getCells().get(row, 1).getStringValue();
                    if (value != null && !value.trim().isEmpty()) {
                        System.out.println("行 " + row + ": '" + value + "'");
                        if (value.contains("2024") && value.contains("新增")) {
                            System.out.println("  --> 找到包含2024和新增的文本!");
                        }
                    }
                }
            } else {
                System.out.println("未找到表6-风险准备金统计表");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}