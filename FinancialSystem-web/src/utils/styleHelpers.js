/**
 * Style Helper Functions for FinancialSystem
 *
 * This file contains utility functions for common style patterns
 * and responsive design helpers.
 */

import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  breakpoints,
} from '../constants/styleConstants';

/**
 * Common Style Patterns
 */

// Form Input Styles
export const getFormInputStyles = (options = {}) => {
  const { error = false, disabled = false, fullWidth = true, size = 'medium' } = options;

  return {
    width: fullWidth ? '100%' : 'auto',
    '& .MuiOutlinedInput-root': {
      fontSize: size === 'small' ? typography.fontSize.xs : typography.fontSize.sm,
      height: size === 'small' ? '28px' : dimensions.height.input,
      backgroundColor: disabled ? colors.background.light : colors.background.paper,
      transition: transitions.all,
      '& input': {
        padding: size === 'small' ? `${spacing.xs} ${spacing.sm}` : `${spacing.sm} ${spacing.md}`,
        height: '100%',
        boxSizing: 'border-box',
      },
      '& fieldset': {
        borderColor: error ? colors.error.main : colors.border.main,
      },
      '&:hover fieldset': {
        borderColor: error ? colors.error.dark : colors.border.dark,
      },
      '&.Mui-focused fieldset': {
        borderColor: error ? colors.error.main : colors.primary.main,
      },
      '&.Mui-disabled': {
        backgroundColor: colors.background.light,
        '& fieldset': {
          borderColor: colors.border.light,
        },
      },
    },
    '& .MuiFormLabel-root': {
      fontSize: typography.fontSize.sm,
      color: error ? colors.error.main : colors.text.secondary,
      '&.Mui-focused': {
        color: error ? colors.error.main : colors.primary.main,
      },
    },
    '& .MuiFormHelperText-root': {
      fontSize: typography.fontSize.xs,
      marginTop: spacing.xs,
      color: error ? colors.error.main : colors.text.secondary,
    },
  };
};

// Button Styles
export const getButtonStyles = (variant = 'contained', color = 'primary', size = 'medium') => {
  const baseStyles = {
    borderRadius: dimensions.borderRadius.base,
    textTransform: 'none',
    fontWeight: typography.fontWeight.medium,
    transition: transitions.all,
    boxShadow: 'none',
  };

  const sizeStyles = {
    small: {
      height: '28px',
      padding: `${spacing.xs} ${spacing.md}`,
      fontSize: typography.fontSize.xs,
    },
    medium: {
      height: dimensions.height.button,
      padding: `${spacing.sm} ${spacing.lg}`,
      fontSize: typography.fontSize.sm,
    },
    large: {
      height: dimensions.height.buttonLarge,
      padding: `${spacing.md} ${spacing.xl}`,
      fontSize: typography.fontSize.base,
    },
  };

  const variantStyles = {
    contained: {
      backgroundColor: colors[color]?.main || color,
      color: colors[color]?.contrastText || '#ffffff',
      '&:hover': {
        backgroundColor: colors[color]?.dark || color,
        boxShadow: shadows.hover,
      },
      '&:disabled': {
        backgroundColor: colors.grey[300],
        color: colors.grey[500],
      },
    },
    outlined: {
      backgroundColor: 'transparent',
      borderColor: colors[color]?.main || color,
      color: colors[color]?.main || color,
      borderWidth: dimensions.borderWidth.base,
      '&:hover': {
        backgroundColor: colors[color]?.main || color,
        color: colors[color]?.contrastText || '#ffffff',
        borderColor: colors[color]?.main || color,
      },
    },
    text: {
      backgroundColor: 'transparent',
      color: colors[color]?.main || color,
      '&:hover': {
        backgroundColor: `${colors[color]?.main || color}14`,
      },
    },
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
};

// Card Styles
export const getCardStyles = (options = {}) => {
  const { elevation = 1, padding = true, hover = false } = options;

  return {
    borderRadius: dimensions.borderRadius.base,
    boxShadow: elevation === 0 ? 'none' : shadows.card,
    backgroundColor: colors.background.paper,
    padding: padding ? spacing.lg : 0,
    transition: transitions.shadow,
    ...(hover && {
      cursor: 'pointer',
      '&:hover': {
        boxShadow: shadows.hover,
      },
    }),
  };
};

// Table Styles
export const getTableStyles = () => ({
  container: {
    borderRadius: dimensions.borderRadius.base,
    overflow: 'hidden',
    boxShadow: shadows.card,
  },
  table: {
    '& .MuiTableHead-root': {
      backgroundColor: colors.background.light,
      '& .MuiTableCell-root': {
        fontWeight: typography.fontWeight.semibold,
        fontSize: typography.fontSize.sm,
        color: colors.text.primary,
        borderBottom: `2px solid ${colors.border.main}`,
      },
    },
    '& .MuiTableBody-root': {
      '& .MuiTableRow-root': {
        transition: transitions.colors,
        '&:hover': {
          backgroundColor: colors.background.hover,
        },
        '&:last-child .MuiTableCell-root': {
          borderBottom: 'none',
        },
      },
      '& .MuiTableCell-root': {
        fontSize: typography.fontSize.sm,
        padding: spacing.sm,
        borderBottom: `1px solid ${colors.border.light}`,
      },
    },
  },
  emptyState: {
    textAlign: 'center',
    padding: spacing['3xl'],
    color: colors.text.secondary,
    fontSize: typography.fontSize.sm,
  },
});

// Section Styles
export const getSectionStyles = (title = true) => ({
  root: {
    marginBottom: spacing.sectionGap,
  },
  title: title
    ? {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      color: colors.secondary.main,
      marginBottom: spacing.md,
    }
    : {},
  content: {
    padding: 0,
  },
});

// Form Section Styles
export const getFormSectionStyles = () => ({
  section: {
    marginBottom: spacing['2xl'],
  },
  sectionTitle: {
    fontSize: typography.fontSize.md,
    fontWeight: typography.fontWeight.medium,
    color: colors.secondary.main,
    marginBottom: spacing.md,
  },
  fieldGroup: {
    display: 'grid',
    gap: spacing.md,
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
  },
  field: {
    marginBottom: 0,
  },
});

/**
 * Responsive Utilities
 */

// Media Query Helper
export const mediaQuery = {
  up: breakpoint => `@media (min-width: ${breakpoints[breakpoint]})`,
  down: breakpoint => `@media (max-width: ${breakpoints[breakpoint]})`,
  between: (min, max) =>
    `@media (min-width: ${breakpoints[min]}) and (max-width: ${breakpoints[max]})`,
};

// Responsive Spacing
export const responsiveSpacing = values => {
  const { xs, sm, md, lg, xl } = values;
  return {
    padding: xs || spacing.md,
    [mediaQuery.up('sm')]: sm && { padding: sm },
    [mediaQuery.up('md')]: md && { padding: md },
    [mediaQuery.up('lg')]: lg && { padding: lg },
    [mediaQuery.up('xl')]: xl && { padding: xl },
  };
};

// Responsive Font Size
export const responsiveFontSize = values => {
  const { xs, sm, md, lg, xl } = values;
  return {
    fontSize: xs || typography.fontSize.base,
    [mediaQuery.up('sm')]: sm && { fontSize: sm },
    [mediaQuery.up('md')]: md && { fontSize: md },
    [mediaQuery.up('lg')]: lg && { fontSize: lg },
    [mediaQuery.up('xl')]: xl && { fontSize: xl },
  };
};

// Responsive Grid
export const responsiveGrid = columns => ({
  display: 'grid',
  gap: spacing.md,
  gridTemplateColumns: '1fr',
  [mediaQuery.up('sm')]: {
    gridTemplateColumns: `repeat(${columns.sm || 2}, 1fr)`,
  },
  [mediaQuery.up('md')]: {
    gridTemplateColumns: `repeat(${columns.md || 3}, 1fr)`,
  },
  [mediaQuery.up('lg')]: {
    gridTemplateColumns: `repeat(${columns.lg || 4}, 1fr)`,
  },
});

/**
 * Animation Helpers
 */

// Fade In Animation
export const fadeIn = (duration = transitions.duration.base) => ({
  animation: `fadeIn ${duration} ${transitions.easing.easeOut}`,
  '@keyframes fadeIn': {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
});

// Slide In Animation
export const slideIn = (direction = 'left', duration = transitions.duration.base) => {
  const transforms = {
    left: 'translateX(-100%)',
    right: 'translateX(100%)',
    top: 'translateY(-100%)',
    bottom: 'translateY(100%)',
  };

  return {
    animation: `slideIn-${direction} ${duration} ${transitions.easing.easeOut}`,
    [`@keyframes slideIn-${direction}`]: {
      from: { transform: transforms[direction], opacity: 0 },
      to: { transform: 'translate(0)', opacity: 1 },
    },
  };
};

// Scale Animation
export const scale = (from = 0.95, to = 1, duration = transitions.duration.fast) => ({
  transition: `transform ${duration} ${transitions.easing.easeOut}`,
  '&:hover': {
    transform: `scale(${to})`,
  },
  '&:active': {
    transform: `scale(${from})`,
  },
});

// Skeleton Loading
export const skeletonLoading = () => ({
  background: `linear-gradient(90deg, ${colors.grey[200]} 25%, ${colors.grey[300]} 50%, ${colors.grey[200]} 75%)`,
  backgroundSize: '200% 100%',
  animation: 'loading 1.5s infinite',
  '@keyframes loading': {
    '0%': { backgroundPosition: '200% 0' },
    '100%': { backgroundPosition: '-200% 0' },
  },
});

/**
 * Utility Functions
 */

// Combine Styles
export const combineStyles = (...styles) => {
  return styles.reduce((acc, style) => ({ ...acc, ...style }), {});
};

// Conditional Styles
export const conditionalStyle = (condition, trueStyle, falseStyle = {}) => {
  return condition ? trueStyle : falseStyle;
};

// Theme Color Helper
export const getThemeColor = (color, shade = 'main') => {
  if (colors[color] && colors[color][shade]) {
    return colors[color][shade];
  }
  return color;
};

// Contrast Text Helper
export const getContrastText = backgroundColor => {
  // Simple implementation - can be enhanced with proper contrast calculation
  const darkColors = ['primary', 'secondary', 'error', 'success', 'info'];
  if (darkColors.some(color => backgroundColor.includes(colors[color]?.main))) {
    return '#ffffff';
  }
  return colors.text.primary;
};

// Export all helpers
export default {
  getFormInputStyles,
  getButtonStyles,
  getCardStyles,
  getTableStyles,
  getSectionStyles,
  getFormSectionStyles,
  mediaQuery,
  responsiveSpacing,
  responsiveFontSize,
  responsiveGrid,
  fadeIn,
  slideIn,
  scale,
  skeletonLoading,
  combineStyles,
  conditionalStyle,
  getThemeColor,
  getContrastText,
};
