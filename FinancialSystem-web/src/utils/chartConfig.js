/**
 * Chart.js 全局配置文件
 * 统一注册所有需要的Chart.js组件，避免"controller not registered"错误
 */

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  // 所有控制器
  BarController,
  LineController,
  DoughnutController,
  PieController,
  PolarAreaController,
  RadarController,
  ScatterController,
  BubbleController,
  // 插件
  Legend,
  Tooltip,
  Title,
  SubTitle,
  Filler,
} from 'chart.js';

// 注册所有Chart.js组件
ChartJS.register(
  // 比例尺
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,

  // 元素
  BarElement,
  LineElement,
  PointElement,
  ArcElement,

  // 控制器
  BarController,
  LineController,
  DoughnutController,
  PieController,
  PolarAreaController,
  RadarController,
  ScatterController,
  BubbleController,

  // 插件
  Legend,
  Tooltip,
  Title,
  SubTitle,
  Filler,
);

// 设置全局默认配置
ChartJS.defaults.responsive = true;
ChartJS.defaults.maintainAspectRatio = false;

export default ChartJS;
