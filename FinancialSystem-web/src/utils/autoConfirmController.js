/**
 * 自动确认控制器
 * 提供简单的全局开关控制，支持语音/文本指令
 */

class AutoConfirmController {
  constructor() {
    this.isEnabled = false;
    this.defaultBehavior = true; // true=自动确认，false=自动拒绝
    this.delay = 500; // 默认延迟500ms
    this.showNotifications = true;

    // 加载保存的设置
    this.loadSettings();

    // 绑定全局函数
    window.enableAutoConfirm = this.enable.bind(this);
    window.disableAutoConfirm = this.disable.bind(this);
    window.toggleAutoConfirm = this.toggle.bind(this);
    window.setAutoConfirmBehavior = this.setBehavior.bind(this);
  }

  /**
   * 加载设置
   */
  loadSettings() {
    try {
      const saved = localStorage.getItem('autoConfirmController');
      if (saved) {
        const settings = JSON.parse(saved);
        this.isEnabled = settings.isEnabled || false;
        this.defaultBehavior =
          settings.defaultBehavior !== undefined ? settings.defaultBehavior : true;
        this.delay = settings.delay || 500;
        this.showNotifications =
          settings.showNotifications !== undefined ? settings.showNotifications : true;
      }
    } catch (error) {
      console.warn('加载自动确认设置失败:', error);
    }
  }

  /**
   * 保存设置
   */
  saveSettings() {
    try {
      const settings = {
        isEnabled: this.isEnabled,
        defaultBehavior: this.defaultBehavior,
        delay: this.delay,
        showNotifications: this.showNotifications,
      };
      localStorage.setItem('autoConfirmController', JSON.stringify(settings));
    } catch (error) {
      console.warn('保存自动确认设置失败:', error);
    }
  }

  /**
   * 启用自动确认
   */
  enable() {
    this.isEnabled = true;
    this.saveSettings();
    this.showMessage('✅ 自动确认已启用', 'success');

    return this;
  }

  /**
   * 禁用自动确认
   */
  disable() {
    this.isEnabled = false;
    this.saveSettings();
    this.showMessage('❌ 自动确认已禁用', 'info');

    return this;
  }

  /**
   * 切换自动确认状态
   */
  toggle() {
    if (this.isEnabled) {
      this.disable();
    } else {
      this.enable();
    }
    return this;
  }

  /**
   * 设置默认行为
   * @param {boolean} autoConfirm - true=自动确认，false=自动拒绝
   */
  setBehavior(autoConfirm = true) {
    this.defaultBehavior = autoConfirm;
    this.saveSettings();
    this.showMessage(`⚙️ 默认行为已设置为: ${autoConfirm ? '自动确认' : '自动拒绝'}`, 'info');

    return this;
  }

  /**
   * 设置延迟时间
   * @param {number} ms - 延迟毫秒数
   */
  setDelay(ms = 500) {
    this.delay = Math.max(0, ms);
    this.saveSettings();
    this.showMessage(`⏱️ 延迟时间已设置为: ${this.delay}ms`, 'info');
    return this;
  }

  /**
   * 设置是否显示通知
   * @param {boolean} show - 是否显示通知
   */
  setNotifications(show = true) {
    this.showNotifications = show;
    this.saveSettings();

    return this;
  }

  /**
   * 自动确认函数 - 替代原生 confirm()
   * @param {string} message - 确认消息
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} - 确认结果
   */
  async autoConfirm(message, options = {}) {
    // 如果未启用，使用原生 confirm
    if (!this.isEnabled) {
      return window.confirm(message);
    }

    const { delay = this.delay, behavior = this.defaultBehavior, showLog = true } = options;

    // 显示日志
    if (showLog) {
    }

    // 显示通知
    if (this.showNotifications) {
      this.showMessage(
        `🤖 ${behavior ? '自动确认' : '自动拒绝'}: ${message.substring(0, 30)}${
          message.length > 30 ? '...' : ''
        }`,
        behavior ? 'success' : 'warning',
        Math.max(delay, 1000),
      );
    }

    // 延迟处理
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    return behavior;
  }

  /**
   * 显示消息通知
   * @param {string} message - 消息内容
   * @param {string} type - 消息类型 (success, warning, info, error)
   * @param {number} duration - 显示时长
   */
  showMessage(message, type = 'info', duration = 2000) {
    if (!this.showNotifications) {
      return;
    }

    const colors = {
      success: '#4caf50',
      warning: '#ff9800',
      info: '#2196f3',
      error: '#f44336',
    };

    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${colors[type] || colors.info};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      z-index: 10000;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      transition: all 0.3s ease;
      max-width: 400px;
      word-wrap: break-word;
      cursor: pointer;
    `;
    toast.textContent = message;

    // 点击关闭
    toast.onclick = () => {
      toast.style.opacity = '0';
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    };

    document.body.appendChild(toast);

    // 自动关闭
    setTimeout(() => {
      if (document.body.contains(toast)) {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
          if (document.body.contains(toast)) {
            document.body.removeChild(toast);
          }
        }, 300);
      }
    }, duration);
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      enabled: this.isEnabled,
      defaultBehavior: this.defaultBehavior,
      delay: this.delay,
      showNotifications: this.showNotifications,
    };
  }

  /**
   * 显示状态
   */
  status() {
    const status = this.getStatus();

    this.showMessage(
      `状态: ${status.enabled ? '启用' : '禁用'} | 行为: ${
        status.defaultBehavior ? '确认' : '拒绝'
      } | 延迟: ${status.delay}ms`,
      'info',
      3000,
    );
    return status;
  }

  /**
   * 显示帮助信息
   */
  help() {
    const commands = [
      'enableAutoConfirm() - 启用自动确认',
      'disableAutoConfirm() - 禁用自动确认',
      'toggleAutoConfirm() - 切换状态',
      'setAutoConfirmBehavior(true/false) - 设置默认行为',
      'autoConfirmController.setDelay(500) - 设置延迟时间',
      'autoConfirmController.status() - 查看当前状态',
      'autoConfirmController.help() - 显示帮助',
    ];

    this.showMessage('帮助信息已在控制台显示', 'info', 3000);
    return commands;
  }
}

// 创建全局实例
const autoConfirmController = new AutoConfirmController();

// 导出实例和便捷函数
export default autoConfirmController;

// 便捷函数
export const autoConfirm = autoConfirmController.autoConfirm.bind(autoConfirmController);
export const enableAutoConfirm = autoConfirmController.enable.bind(autoConfirmController);
export const disableAutoConfirm = autoConfirmController.disable.bind(autoConfirmController);
export const toggleAutoConfirm = autoConfirmController.toggle.bind(autoConfirmController);
export const setAutoConfirmBehavior = autoConfirmController.setBehavior.bind(autoConfirmController);

// 挂载到全局 window 对象
window.autoConfirmController = autoConfirmController;
