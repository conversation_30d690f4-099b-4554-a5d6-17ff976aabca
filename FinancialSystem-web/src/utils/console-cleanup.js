/**
 * Console cleanup utility for production
 * This file helps remove console statements from production build
 */

// Override console methods in production
if (process.env.NODE_ENV === 'production') {
  console.warn = () => {};
  // Keep console.error for critical error reporting
  const originalError = console.error;
  console.error = (...args) => {
    // Only log critical errors, not development debug info
    if (args.length > 0 && typeof args[0] === 'string' && args[0].includes('React Error')) {
      originalError.apply(console, args);
    }
  };
}

const consoleCleanup = {
  // Console cleanup utility object
  init() {
    // Initialize console cleanup
  },
};

export default consoleCleanup;
