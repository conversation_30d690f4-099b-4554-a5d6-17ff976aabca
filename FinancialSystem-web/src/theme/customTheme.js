/**
 * Custom Theme Configuration for FinancialSystem
 *
 * This file extends the Material Dashboard 2 theme with custom configurations
 * based on the unified style constants.
 */

import { createTheme } from '@mui/material/styles';
import baseTheme from '../assets/theme';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  breakpoints,
} from '../constants/styleConstants';

// Helper function to convert pixel values to rem
export const pxToRem = px => {
  const baseFontSize = 16;
  return `${px / baseFontSize}rem`;
};

// Create custom theme by extending the base theme
const customTheme = createTheme(baseTheme, {
  // Override palette with our custom colors
  palette: {
    primary: {
      main: colors.primary.main,
      light: colors.primary.light,
      dark: colors.primary.dark,
      contrastText: colors.primary.contrastText,
    },
    secondary: {
      main: colors.secondary.main,
      light: colors.secondary.light,
      dark: colors.secondary.dark,
      contrastText: colors.secondary.contrastText,
    },
    error: {
      main: colors.error.main,
      light: colors.error.light,
      dark: colors.error.dark,
      contrastText: colors.error.contrastText,
    },
    warning: {
      main: colors.warning.main,
      light: colors.warning.light,
      dark: colors.warning.dark,
      contrastText: colors.warning.contrastText,
    },
    success: {
      main: colors.success.main,
      light: colors.success.light,
      dark: colors.success.dark,
      contrastText: colors.success.contrastText,
    },
    info: {
      main: colors.info.main,
      light: colors.info.light,
      dark: colors.info.dark,
      contrastText: colors.info.contrastText,
    },
    grey: colors.grey,
    text: colors.text,
    background: colors.background,
    divider: colors.border.main,
  },

  // Override typography
  typography: {
    fontFamily: typography.fontFamily.base,
    fontSize: parseInt(typography.fontSize.base),
    h1: {
      fontSize: typography.fontSize['5xl'],
      fontWeight: typography.fontWeight.bold,
      lineHeight: typography.lineHeight.tight,
    },
    h2: {
      fontSize: typography.fontSize['4xl'],
      fontWeight: typography.fontWeight.bold,
      lineHeight: typography.lineHeight.tight,
    },
    h3: {
      fontSize: typography.fontSize['3xl'],
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.lineHeight.normal,
    },
    h4: {
      fontSize: typography.fontSize['2xl'],
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.lineHeight.normal,
    },
    h5: {
      fontSize: typography.fontSize.xl,
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.lineHeight.normal,
    },
    h6: {
      fontSize: typography.fontSize.lg,
      fontWeight: typography.fontWeight.semibold,
      lineHeight: typography.lineHeight.normal,
    },
    subtitle1: {
      fontSize: typography.fontSize.md,
      fontWeight: typography.fontWeight.medium,
      lineHeight: typography.lineHeight.normal,
    },
    subtitle2: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      lineHeight: typography.lineHeight.normal,
    },
    body1: {
      fontSize: typography.fontSize.base,
      fontWeight: typography.fontWeight.regular,
      lineHeight: typography.lineHeight.normal,
    },
    body2: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.regular,
      lineHeight: typography.lineHeight.normal,
    },
    button: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      textTransform: 'none',
    },
    caption: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.regular,
      lineHeight: typography.lineHeight.normal,
    },
  },

  // Override spacing
  spacing: spacing.unit,

  // Override shape
  shape: {
    borderRadius: parseInt(dimensions.borderRadius.base),
  },

  // Override shadows
  shadows: [
    shadows.none,
    shadows.xs,
    shadows.sm,
    shadows.base,
    shadows.md,
    shadows.lg,
    shadows.xl,
    shadows.card,
    shadows.hover,
    shadows.input,
    shadows.button,
    shadows.dropdown,
    shadows.modal,
    // Fill remaining slots with default shadows
    ...Array(12).fill(shadows.lg),
  ],

  // Override transitions
  transitions: {
    duration: {
      shortest: parseInt(transitions.duration.fast),
      shorter: parseInt(transitions.duration.base),
      short: parseInt(transitions.duration.base),
      standard: parseInt(transitions.duration.base),
      complex: parseInt(transitions.duration.slow),
      enteringScreen: parseInt(transitions.duration.base),
      leavingScreen: parseInt(transitions.duration.fast),
    },
    easing: transitions.easing,
  },

  // Override breakpoints
  breakpoints: {
    values: {
      xs: parseInt(breakpoints.xs),
      sm: parseInt(breakpoints.sm),
      md: parseInt(breakpoints.md),
      lg: parseInt(breakpoints.lg),
      xl: parseInt(breakpoints.xl),
    },
  },

  // Component overrides
  components: {
    // Button overrides
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: dimensions.borderRadius.base,
          textTransform: 'none',
          fontWeight: typography.fontWeight.medium,
          fontSize: typography.fontSize.sm,
          transition: transitions.all,
          '&:hover': {
            boxShadow: shadows.hover,
          },
        },
        sizeMedium: {
          height: dimensions.height.button,
          padding: `${spacing.sm} ${spacing.lg}`,
        },
        sizeLarge: {
          height: dimensions.height.buttonLarge,
          padding: `${spacing.md} ${spacing.xl}`,
          fontSize: typography.fontSize.base,
        },
        sizeSmall: {
          height: '28px',
          padding: `${spacing.xs} ${spacing.md}`,
          fontSize: typography.fontSize.xs,
        },
        contained: {
          boxShadow: shadows.button,
          '&:hover': {
            boxShadow: shadows.hover,
          },
        },
        outlined: {
          borderWidth: dimensions.borderWidth.base,
        },
      },
    },

    // TextField overrides
    MuiTextField: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            fontSize: typography.fontSize.sm,
            '& input': {
              padding: `${spacing.sm} ${spacing.md}`,
              height: dimensions.height.input,
              boxSizing: 'border-box',
            },
          },
        },
      },
    },

    // Paper overrides
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
        rounded: {
          borderRadius: dimensions.borderRadius.base,
        },
        elevation1: {
          boxShadow: shadows.card,
        },
        elevation2: {
          boxShadow: shadows.md,
        },
        elevation3: {
          boxShadow: shadows.lg,
        },
      },
    },

    // Card overrides
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: dimensions.borderRadius.base,
          boxShadow: shadows.card,
          transition: transitions.shadow,
          '&:hover': {
            boxShadow: shadows.hover,
          },
        },
      },
    },

    // Table overrides
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: spacing.sm,
          fontSize: typography.fontSize.sm,
          borderBottom: `${dimensions.borderWidth.thin} solid ${colors.border.light}`,
        },
        head: {
          fontWeight: typography.fontWeight.semibold,
          backgroundColor: colors.background.light,
          color: colors.text.primary,
        },
      },
    },

    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: colors.background.hover,
          },
          '&.Mui-selected': {
            backgroundColor: colors.background.selected,
            '&:hover': {
              backgroundColor: colors.background.selected,
            },
          },
        },
      },
    },

    // Select overrides
    MuiSelect: {
      styleOverrides: {
        select: {
          fontSize: typography.fontSize.sm,
          padding: `${spacing.sm} ${spacing.md}`,
          minHeight: dimensions.height.input,
          lineHeight: 'normal',
          '&:focus': {
            backgroundColor: 'transparent',
          },
        },
      },
    },

    // Menu overrides
    MuiMenuItem: {
      styleOverrides: {
        root: {
          fontSize: typography.fontSize.sm,
          padding: `${spacing.sm} ${spacing.lg}`,
          '&:hover': {
            backgroundColor: colors.background.hover,
          },
          '&.Mui-selected': {
            backgroundColor: colors.background.selected,
            '&:hover': {
              backgroundColor: colors.background.selected,
            },
          },
        },
      },
    },

    // Input overrides
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          fontSize: typography.fontSize.sm,
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.border.main,
            transition: transitions.colors,
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.border.dark,
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: colors.primary.main,
          },
        },
        input: {
          padding: `${spacing.sm} ${spacing.md}`,
          height: dimensions.height.input,
          boxSizing: 'border-box',
        },
      },
    },

    // Typography overrides
    MuiTypography: {
      styleOverrides: {
        root: {
          color: colors.text.primary,
        },
        h5: {
          color: colors.secondary.dark,
        },
        h6: {
          color: colors.secondary.main,
        },
      },
    },

    // Divider overrides
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: colors.border.main,
        },
      },
    },

    // Container overrides
    MuiContainer: {
      styleOverrides: {
        root: {
          paddingLeft: spacing.containerPadding,
          paddingRight: spacing.containerPadding,
        },
        maxWidthLg: {
          maxWidth: dimensions.width.maxContent,
        },
      },
    },

    // Tooltip overrides
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: colors.grey[800],
          fontSize: typography.fontSize.xs,
          padding: `${spacing.xs} ${spacing.sm}`,
          borderRadius: dimensions.borderRadius.sm,
        },
      },
    },

    // Alert overrides
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: dimensions.borderRadius.base,
          fontSize: typography.fontSize.sm,
        },
      },
    },

    // Chip overrides
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: dimensions.borderRadius.full,
          fontSize: typography.fontSize.xs,
          height: '24px',
        },
      },
    },
  },
});

export default customTheme;
