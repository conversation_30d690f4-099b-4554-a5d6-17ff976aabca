import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Box,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Container,
  Divider,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  GetApp as GetAppIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  CloudDownload as CloudDownloadIcon,
} from '@mui/icons-material';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { zhCN } from 'date-fns/locale';
import api from 'utils/api';
import MDButton from 'components/MDButton';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';

const OAWorkflowExtractor = () => {
  const styles = {
    paper: {
      padding: 2,
      margin: '10px 0',
      borderRadius: 1,
      boxShadow: '0 2px 8px 0 rgba(0,0,0,0.1)',
      backgroundColor: '#ffffff',
    },
    title: {
      fontSize: '18px',
      fontWeight: 600,
      color: '#1a237e',
      marginBottom: 1.5,
      textAlign: 'center',
    },
    sectionTitle: {
      fontSize: '15px',
      fontWeight: 500,
      color: '#283593',
      marginBottom: 1,
    },
  };

  // 状态管理
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    workflowType: '日常留言',
    startDate: new Date(2025, 5, 1), // 6月1日
    endDate: new Date(2025, 5, 30), // 6月30日
    outputFormat: 'PDF',
    includeScreenshot: true,
    includeHtml: false,
  });

  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [files, setFiles] = useState([]);
  const [filesLoading, setFilesLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [progress, setProgress] = useState(0);

  // 组件挂载时获取文件列表
  useEffect(() => {
    fetchFiles();
  }, []);

  // 处理表单输入变化
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // 提取工作流
  const handleExtract = async () => {
    if (!formData.username || !formData.password) {
      setError('请输入用户名和密码');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);
    setProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 1000);

      const response = await api.post('/oa/workflow/extract-sync', {
        username: formData.username,
        password: formData.password,
        workflowType: formData.workflowType,
        startDate: formData.startDate.toISOString().split('T')[0],
        endDate: formData.endDate.toISOString().split('T')[0],
        outputFormat: formData.outputFormat,
        includeScreenshot: formData.includeScreenshot,
        includeHtml: formData.includeHtml,
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (response.data.success) {
        setResult(response.data);
        // 刷新文件列表
        await fetchFiles();
      } else {
        setError(response.data.errorMessage || '提取失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  // 快速提取6月份数据
  const handleQuickExtractJune = async () => {
    if (!formData.username || !formData.password) {
      setError('请输入用户名和密码');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await api.post('/oa/workflow/extract-june', {
        username: formData.username,
        password: formData.password,
      });

      if (response.data.success) {
        setResult(response.data);
        await fetchFiles();
      } else {
        setError(response.data.errorMessage || '提取失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取文件列表
  const fetchFiles = async () => {
    setFilesLoading(true);
    try {
      const response = await api.get('/oa/workflow/files');
      if (response.data.success) {
        setFiles(response.data.files);
      }
    } catch (err) {
      console.error('获取文件列表失败:', err);
    } finally {
      setFilesLoading(false);
    }
  };

  // 下载文件
  const handleDownload = async fileName => {
    try {
      const response = await api.get(`/oa/workflow/download/${fileName}`, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('下载失败: ' + err.message);
    }
  };

  // 批量下载
  const handleBatchDownload = async () => {
    if (files.length === 0) {
      setError('没有可下载的文件');
      return;
    }

    try {
      const fileNames = files.map(file => file.fileName);
      const response = await api.post('/oa/workflow/download-batch', fileNames, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `oa_workflows_${new Date().getTime()}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('批量下载失败: ' + err.message);
    }
  };

  // 删除文件
  const handleDelete = async fileName => {
    try {
      await api.delete(`/oa/workflow/files/${fileName}`);
      setDeleteDialogOpen(false);
      setFileToDelete(null);
      await fetchFiles();
    } catch (err) {
      setError('删除失败: ' + err.message);
    }
  };

  // 获取文件图标
  const getFileIcon = fileName => {
    const extension = fileName.split('.').pop().toLowerCase();
    if (extension === 'pdf') {
      return <PdfIcon color="error" />;
    } else if (['png', 'jpg', 'jpeg'].includes(extension)) {
      return <ImageIcon color="primary" />;
    }
    return <GetAppIcon />;
  };

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Container maxWidth="xl">
        <Box sx={{ mt: 2, mb: 3 }}>
          <Paper sx={styles.paper} elevation={0}>
            <Typography variant="h5" sx={styles.title}>
              OA工作流提取工具
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mb: 3 }}>
              自动提取OA系统中的日常留言工作流，并保存为PDF格式
            </Typography>

            <Grid container spacing={3}>
              {/* 主要操作区域 */}
              <Grid item xs={12} lg={8}>
                <Box>
                  <Typography variant="h6" sx={styles.sectionTitle}>
                    提取设置
                  </Typography>

                  <Box mt={2}>
                    <Grid container spacing={2}>
                      {/* 登录信息 */}
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="OA系统用户名"
                          value={formData.username}
                          onChange={e => handleInputChange('username', e.target.value)}
                          variant="outlined"
                          required
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="OA系统密码"
                          type="password"
                          value={formData.password}
                          onChange={e => handleInputChange('password', e.target.value)}
                          variant="outlined"
                          required
                        />
                      </Grid>

                      {/* 工作流类型 */}
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>工作流类型</InputLabel>
                          <Select
                            value={formData.workflowType}
                            label="工作流类型"
                            onChange={e => handleInputChange('workflowType', e.target.value)}
                          >
                            <MenuItem value="日常留言">日常留言</MenuItem>
                            <MenuItem value="">所有类型</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      {/* 输出格式 */}
                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>输出格式</InputLabel>
                          <Select
                            value={formData.outputFormat}
                            label="输出格式"
                            onChange={e => handleInputChange('outputFormat', e.target.value)}
                          >
                            <MenuItem value="PDF">PDF</MenuItem>
                            <MenuItem value="PNG">截图</MenuItem>
                            <MenuItem value="HTML">网页</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      {/* 时间范围 */}
                      <Grid item xs={12} md={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="开始日期"
                            value={formData.startDate}
                            onChange={date => handleInputChange('startDate', date)}
                            renderInput={params => <TextField {...params} fullWidth />}
                          />
                        </LocalizationProvider>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={zhCN}>
                          <DatePicker
                            label="结束日期"
                            value={formData.endDate}
                            onChange={date => handleInputChange('endDate', date)}
                            renderInput={params => <TextField {...params} fullWidth />}
                          />
                        </LocalizationProvider>
                      </Grid>
                    </Grid>

                    {/* 进度条 */}
                    {loading && (
                      <Box mt={2}>
                        <LinearProgress variant="determinate" value={progress} />
                        <Typography variant="body2" color="text.secondary" mt={1}>
                          正在提取工作流... {Math.round(progress)}%
                        </Typography>
                      </Box>
                    )}

                    {/* 错误提示 */}
                    {error && (
                      <Alert severity="error" sx={{ mt: 2 }}>
                        {error}
                      </Alert>
                    )}

                    {/* 成功结果 */}
                    {result && result.success && (
                      <Alert severity="success" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          成功提取 {result.processedCount}/{result.totalCount} 个工作流
                        </Typography>
                        <Typography variant="body2">
                          执行时间: {result.executionTimeDescription}
                        </Typography>
                      </Alert>
                    )}

                    {/* 操作按钮 */}
                    <Box mt={3} display="flex" gap={2}>
                      <MDButton
                        variant="gradient"
                        color="primary"
                        onClick={handleExtract}
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} /> : <CloudDownloadIcon />}
                      >
                        {loading ? '正在提取...' : '开始提取'}
                      </MDButton>

                      <MDButton
                        variant="outlined"
                        color="secondary"
                        onClick={handleQuickExtractJune}
                        disabled={loading}
                      >
                        快速提取6月份
                      </MDButton>
                    </Box>
                  </Box>
                </Box>
              </Grid>

              {/* 侧边栏 - 统计信息 */}
              <Grid item xs={12} lg={4}>
                <Box>
                  <Typography variant="h6" sx={styles.sectionTitle}>
                    提取统计
                  </Typography>

                  {result && (
                    <Box>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">总数:</Typography>
                        <Chip label={result.totalCount} size="small" />
                      </Box>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">成功:</Typography>
                        <Chip label={result.processedCount} color="success" size="small" />
                      </Box>
                      <Box display="flex" justifyContent="space-between" mb={1}>
                        <Typography variant="body2">失败:</Typography>
                        <Chip label={result.failedCount} color="error" size="small" />
                      </Box>
                      <Box display="flex" justifyContent="space-between">
                        <Typography variant="body2">成功率:</Typography>
                        <Chip
                          label={`${result.successRate?.toFixed(1)}%`}
                          color="info"
                          size="small"
                        />
                      </Box>
                    </Box>
                  )}

                  <Box mt={3}>
                    <Typography variant="h6" sx={styles.sectionTitle}>
                      文件管理
                    </Typography>
                    <Box display="flex" gap={1} flexDirection="column">
                      <MDButton
                        variant="outlined"
                        size="small"
                        onClick={fetchFiles}
                        disabled={filesLoading}
                        startIcon={<RefreshIcon />}
                      >
                        刷新列表
                      </MDButton>
                      <MDButton
                        variant="outlined"
                        size="small"
                        onClick={handleBatchDownload}
                        disabled={files.length === 0}
                        startIcon={<DownloadIcon />}
                      >
                        批量下载
                      </MDButton>
                    </Box>
                  </Box>
                </Box>
              </Grid>

              {/* 文件列表 */}
              <Grid item xs={12}>
                <Box mt={3}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <Typography variant="h6" sx={styles.sectionTitle}>
                      已提取的文件 ({files.length})
                    </Typography>
                    <IconButton onClick={fetchFiles} disabled={filesLoading}>
                      <RefreshIcon />
                    </IconButton>
                  </Box>

                  {filesLoading ? (
                    <Box display="flex" justifyContent="center" p={3}>
                      <CircularProgress />
                    </Box>
                  ) : files.length === 0 ? (
                    <Typography variant="body2" color="text.secondary" align="center" py={3}>
                      暂无文件
                    </Typography>
                  ) : (
                    <TableContainer component={Paper}>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>文件名</TableCell>
                            <TableCell>大小</TableCell>
                            <TableCell>修改时间</TableCell>
                            <TableCell align="center">操作</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {files.map((file, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <Box display="flex" alignItems="center" gap={1}>
                                  {getFileIcon(file.fileName)}
                                  <Typography variant="body2">{file.fileName}</Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">{file.fileSizeFormatted}</Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">
                                  {new Date(file.lastModified).toLocaleString()}
                                </Typography>
                              </TableCell>
                              <TableCell align="center">
                                <IconButton
                                  size="small"
                                  onClick={() => handleDownload(file.fileName)}
                                  title="下载"
                                >
                                  <DownloadIcon />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  onClick={() => {
                                    setFileToDelete(file.fileName);
                                    setDeleteDialogOpen(true);
                                  }}
                                  title="删除"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  )}
                </Box>
              </Grid>
            </Grid>

            {/* 删除确认对话框 */}
            <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
              <DialogTitle>确认删除</DialogTitle>
              <DialogContent>
                <Typography>
                  确定要删除文件 &ldquo;{fileToDelete}&rdquo; 吗？此操作无法撤销。
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
                <Button
                  onClick={() => handleDelete(fileToDelete)}
                  color="error"
                  variant="contained"
                >
                  删除
                </Button>
              </DialogActions>
            </Dialog>
          </Paper>
        </Box>
      </Container>
    </DashboardLayout>
  );
};

export default OAWorkflowExtractor;
