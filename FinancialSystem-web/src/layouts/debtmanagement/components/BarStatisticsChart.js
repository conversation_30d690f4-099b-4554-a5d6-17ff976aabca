import React from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar } from 'react-chartjs-2';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 债权统计柱状图组件（按公司分类）
 * @param {Object} props
 * @param {Array} props.data - 数据数组，每项包含 { companyName, newAmount, reductionAmount }
 * @param {String} props.chartTitle - 图表标题
 * @param {String} props.chartDebtLabel - 第一个柱状图的标签
 * @returns {JSX.Element}
 */
const BarStatisticsChart = ({ data, chartTitle, chartDebtLabel }) => {
  // 1. 根据 newAmountSum 做降序
  const sortedData = [...data].sort((a, b) => (b.newAmount || 0) - (a.newAmount || 0));

  // 2. 拿到公司名称数组
  const labels = sortedData.map(item => item.companyName);

  // 3. 组装 chart.js 数据结构
  const chartData = {
    labels,
    datasets: [
      {
        label: '新增债权',
        data: sortedData.map(item => item.newAmount),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
        borderWidth: 1,
      },
      {
        label: '处置债权',
        data: sortedData.map(item => item.reductionAmount),
        backgroundColor: 'rgba(255, 99, 132, 0.6)',
        borderWidth: 1,
      },
    ],
  };

  // 4. 配置图表选项
  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: chartTitle,
      },
      datalabels: {
        anchor: 'end',
        align: 'start',
        offset: -5,
        color: '#000',
        font: {
          size: 9,
        },
        formatter(value) {
          return Math.round(value).toLocaleString();
        },
      },
    },
    scales: {
      y: {
        type: 'linear',
        title: {
          display: true,
          text: '金额 (万元)',
        },
        beginAtZero: true,
      },
      x: {
        grid: {
          display: false, // 移除X轴网格线
        },
      },
    },
    maintainAspectRatio: false,
  };

  return (
    <div
      style={{
        minWidth: '600px',
        width: '100%',
        maxWidth: '100%',
        marginBottom: '20px',
        height: '350px',
      }}
      className="p-6 bg-white rounded-lg shadow-md"
    >
      <Bar data={chartData} options={options} style={{ height: '100%', width: '100%' }} />
    </div>
  );
};

BarStatisticsChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      companyName: PropTypes.string.isRequired,
      newAmount: PropTypes.number.isRequired,
      reductionAmount: PropTypes.number.isRequired,
    }),
  ).isRequired,
  chartTitle: PropTypes.string.isRequired,
  chartDebtLabel: PropTypes.string.isRequired,
};

export default BarStatisticsChart;
