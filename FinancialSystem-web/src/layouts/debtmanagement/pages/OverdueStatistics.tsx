import React, { useEffect, useState, useCallback } from 'react';
import { Box, Grid, Typography, Button } from '@mui/material';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import OverdueStatisticsFilter from '../components/OverdueStatisticsFilter';
import { fetchDebtStatistics, fetchDebtStatisticsDetail } from '../data/DebtStatisticsData';
import DebtStatisticsChart from '../components/DebtStatisticsChart';
import DownloadExcelButton from '../components/DownloadExcelButton';

// 为JavaScript组件声明类型
interface DownloadExcelButtonProps {
  type: 'NewDebtDetails' | 'ReductionDebtDetails';
  buttonText?: string;
  year?: string;
  month?: string;
  company?: string;
}

// 类型声明
declare const DownloadExcelButtonTyped: React.FC<DownloadExcelButtonProps>;
import <PERSON><PERSON>ie<PERSON><PERSON> from '../components/CustomPieChart';
import BarStatistics<PERSON>hart from '../components/BarStatisticsChart';
import GenericDataTable from 'components/tables/GenericDataTable';
import { DebtStatistics, RequestStatus } from '@/types';

/**
 * 筛选条件接口
 */
interface FilterConditions {
  year: string;
  month: string;
  company: string;
}

/**
 * 债权统计数据接口
 */
interface DebtStatisticsData {
  totalReductionAmount: number;
  totalDebtBalance: number;
  initialDebtBalance: number;
  initialDebtReductionAmount: number;
  initialDebtEndingBalance: number;
  newDebtAmount: number;
  newDebtReductionAmount: number;
  newDebtBalance: number;
  newDebtSummaryByCompany: CompanySummary[];
  existingDebtSummaryByCompany: CompanySummary[];
  monthNewReductionDebtByCompany: CompanySummary[];
}

/**
 * 公司统计数据接口
 */
interface CompanySummary {
  companyName: string;
  amount: number;
  count: number;
  percentage?: number;
}

/**
 * 逾期债权统计看板页面
 */
const OverdueStatistics: React.FC = () => {
  // 债权统计数据状态
  const [debtStatistics, setDebtStatistics] = useState<DebtStatisticsData>({
    totalReductionAmount: 0.0,
    totalDebtBalance: 0.0,
    initialDebtBalance: 0.0,
    initialDebtReductionAmount: 0.0,
    initialDebtEndingBalance: 0.0,
    newDebtAmount: 0.0,
    newDebtReductionAmount: 0.0,
    newDebtBalance: 0.0,
    newDebtSummaryByCompany: [],
    existingDebtSummaryByCompany: [],
    monthNewReductionDebtByCompany: []
  });

  // 当前筛选条件状态
  const [currentFilters, setCurrentFilters] = useState<FilterConditions>({
    year: new Date().getFullYear().toString(),
    month: (new Date().getMonth() + 1).toString().padStart(2, '0') + '月',
    company: '全部'
  });

  // 加载状态
  const [loading, setLoading] = useState<RequestStatus>(RequestStatus.IDLE);
  const [error, setError] = useState<string | null>(null);

  /**
   * 获取初始数据
   */
  const fetchInitialData = useCallback(async () => {
    setLoading(RequestStatus.LOADING);
    setError(null);

    try {
      // 获取当前日期作为默认值
      const currentDate = new Date();
      const defaultYear = currentDate.getFullYear().toString();
      const defaultMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0') + '月';
      const defaultCompany = '全部'; // 默认查询所有公司

      // 设置初始筛选条件
      const initialFilters: FilterConditions = {
        year: defaultYear,
        month: defaultMonth,
        company: defaultCompany
      };

      setCurrentFilters(initialFilters);

      // 调用API获取数据
      const response = await fetchDebtStatistics(defaultYear, defaultMonth, defaultCompany);

      if (response && response.success) {
        setDebtStatistics(response.data);
        setLoading(RequestStatus.SUCCESS);
      } else {
        throw new Error(response?.message || '获取数据失败');
      }
    } catch (err) {
      console.error('获取初始数据失败:', err);
      setError(err instanceof Error ? err.message : '获取数据失败');
      setLoading(RequestStatus.ERROR);
    }
  }, []);

  /**
   * 处理筛选条件变化
   */
  const handleFilterChange = useCallback(async (year: string, month: string, company: string) => {
    setLoading(RequestStatus.LOADING);
    setError(null);

    try {
      const filters: FilterConditions = { year, month, company };
      setCurrentFilters(filters);

      const response = await fetchDebtStatistics(year, month, company);

      if (response && response.success) {
        setDebtStatistics(response.data);
        setLoading(RequestStatus.SUCCESS);
      } else {
        throw new Error(response?.message || '获取数据失败');
      }
    } catch (err) {
      console.error('筛选数据失败:', err);
      setError(err instanceof Error ? err.message : '筛选数据失败');
      setLoading(RequestStatus.ERROR);
    }
  }, []);

  /**
   * 格式化金额显示
   */
  const formatAmount = useCallback((amount: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }, []);

  /**
   * 计算百分比
   */
  const calculatePercentage = useCallback((value: number, total: number): number => {
    return total > 0 ? Math.round((value / total) * 100 * 100) / 100 : 0;
  }, []);

  // 组件挂载时获取初始数据
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  /**
   * 处理数据导出
   */
  const handleExport = useCallback(() => {
    // 导出逻辑实现
  }, [currentFilters, debtStatistics]);

  /**
   * 渲染加载状态
   */
  const renderLoadingState = () => (
    <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
      <Typography variant="h6" color="textSecondary">
        加载中...
      </Typography>
    </Box>
  );

  /**
   * 渲染错误状态
   */
  const renderErrorState = () => (
    <Box display="flex" flexDirection="column" alignItems="center" minHeight="200px" p={3}>
      <Typography variant="h6" color="error" gutterBottom>
        {error}
      </Typography>
      <Button variant="contained" onClick={fetchInitialData} sx={{ mt: 2 }}>
        重新加载
      </Button>
    </Box>
  );

  /**
   * 渲染统计卡片
   */
  const renderStatisticsCards = () => (
    <Grid container spacing={3} mb={3}>
      <Grid item xs={12} md={3}>
        <Box
          sx={{
            p: 2,
            bgcolor: 'primary.main',
            color: 'white',
            borderRadius: 2,
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" fontWeight="bold">
            {formatAmount(debtStatistics.totalDebtBalance)}
          </Typography>
          <Typography variant="subtitle1">债权余额总计</Typography>
        </Box>
      </Grid>

      <Grid item xs={12} md={3}>
        <Box
          sx={{
            p: 2,
            bgcolor: 'success.main',
            color: 'white',
            borderRadius: 2,
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" fontWeight="bold">
            {formatAmount(debtStatistics.totalReductionAmount)}
          </Typography>
          <Typography variant="subtitle1">累计处置金额</Typography>
        </Box>
      </Grid>

      <Grid item xs={12} md={3}>
        <Box
          sx={{
            p: 2,
            bgcolor: 'warning.main',
            color: 'white',
            borderRadius: 2,
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" fontWeight="bold">
            {formatAmount(debtStatistics.newDebtAmount)}
          </Typography>
          <Typography variant="subtitle1">新增债权金额</Typography>
        </Box>
      </Grid>

      <Grid item xs={12} md={3}>
        <Box
          sx={{
            p: 2,
            bgcolor: 'info.main',
            color: 'white',
            borderRadius: 2,
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" fontWeight="bold">
            {calculatePercentage(
              debtStatistics.totalReductionAmount,
              debtStatistics.totalDebtBalance
            )}
            %
          </Typography>
          <Typography variant="subtitle1">处置完成率</Typography>
        </Box>
      </Grid>
    </Grid>
  );

  return (
    <DashboardLayout>
      <DashboardNavbar />
      <Box sx={{ p: 3 }}>
        {/* 页面标题和操作按钮 */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" fontWeight="bold">
            逾期债权统计看板
          </Typography>
          <Box>
            <DownloadExcelButton
              {...({
                type: 'NewDebtDetails',
                buttonText: '导出Excel报表',
                year: currentFilters.year,
                month: currentFilters.month,
                company: currentFilters.company
              } as any)}
            />
          </Box>
        </Box>

        {/* 筛选条件 */}
        <OverdueStatisticsFilter onSearch={handleFilterChange} />

        {/* 内容区域 */}
        {loading === RequestStatus.LOADING && renderLoadingState()}
        {loading === RequestStatus.ERROR && renderErrorState()}
        {loading === RequestStatus.SUCCESS && (
          <>
            {/* 统计卡片 */}
            {renderStatisticsCards()}

            {/* 图表区域 */}
            <Grid container spacing={3} mb={3}>
              <Grid item xs={12} md={6}>
                <CustomPieChart
                  title="债权分布情况"
                  data={debtStatistics.newDebtSummaryByCompany}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <BarStatisticsChart
                  chartTitle="公司债权对比"
                  chartDebtLabel="债权金额"
                  data={debtStatistics.existingDebtSummaryByCompany}
                />
              </Grid>
            </Grid>

            {/* 趋势图 */}
            <Grid container spacing={3} mb={3}>
              <Grid item xs={12}>
                <DebtStatisticsChart
                  data={debtStatistics.existingDebtSummaryByCompany}
                  chartTitle="债权变化趋势"
                  chartDebtLabel="债权金额"
                />
              </Grid>
            </Grid>

            {/* 数据表格 */}
            <GenericDataTable
              columns={[
                { field: 'companyName', headerName: '公司名称', width: '30%' },
                { field: 'amount', headerName: '金额', width: '25%', type: 'number' },
                { field: 'count', headerName: '数量', width: '20%', type: 'number' },
                { field: 'percentage', headerName: '占比', width: '25%', type: 'number' }
              ]}
              data={debtStatistics.monthNewReductionDebtByCompany}
            />
          </>
        )}
      </Box>
    </DashboardLayout>
  );
};

export default OverdueStatistics;
