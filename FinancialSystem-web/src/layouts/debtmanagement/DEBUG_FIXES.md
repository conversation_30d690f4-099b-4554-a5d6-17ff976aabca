# 逾期债权统计页面修复说明

## 修复时间

2025-08-19

## 问题描述

1. **标题错误**：各子公司新增债权图表显示为"各单位新增债权余额"
2. **数据缺失**：图表显示"暂无数据"
3. **新增债权清收情况**：只有累计新增数据，其他维度都是 0

## 修复内容

### 1. 标题修复 (OverdueStatistics.js)

- **位置**：第 686 行
- **修改前**：`title="各单位新增债权余额"`
- **修改后**：`title="各子公司新增债权完成情况"`

### 2. 数据映射优化 (OverdueStatistics.js)

- **位置**：第 665-685 行
- 增强了数据字段映射逻辑，支持多种可能的字段名：
  - 公司名：`company`、`companyName`、`管理公司`
  - 新增金额：`newDebtAmount`、`新增债权金额`、`新增金额`
  - 处置金额：`disposalAmount`、`reductionAmount`、`处置金额`、`累计处置金额`
  - 余额：`remainingBalance`、`balance`、`债权余额`、`余额`

### 3. 调试日志增强

添加了详细的调试日志以便追踪数据流：

#### DebtStatisticsData.js

- 新增债权统计数据详情日志（第 289-296 行）
- 各单位新增债权余额统计详情日志（第 322-327 行）

#### NewDebtBalanceChart.js

- 原始数据接收日志（第 39 行）
- 有效数据过滤日志（第 51 行）

#### OverdueStatistics.js

- 数据获取时的详细日志（第 249-251 行，第 347-350 行）
- 数据映射过程日志（第 668 行，第 681 行）

### 4. 新增债权清收数据完善 (OverdueStatistics.js)

- **位置**：第 563-595 行
- 优化了 NewDebtCollectionChart 的数据传递逻辑
- 增强了字段名兼容性：
  - 本月新增：`monthNewAmount`
  - 累计新增：`cumulativeNewAmount`、`totalNewAmount`
  - 本月处置：`monthDisposalAmount`
  - 累计处置：`cumulativeDisposalAmount`、`totalReductionAmount`
  - 期末余额：`periodEndBalance`、`currentBalance`

## 调试步骤

### 1. 检查浏览器控制台

打开浏览器开发者工具，查看以下日志：

- "新增债权统计数据详情"
- "各单位新增债权余额统计获取成功"
- "响应数据详情"
- "处理公司数据"
- "映射后的数据"
- "NewDebtBalanceChart 接收到的原始数据"

### 2. 验证 API 响应

确认后端 API 返回的数据格式：

- `/debts/statistics/new-debt-statistics`
- `/debts/statistics/new-debt-balance-by-company`
- `/debts/statistics/new-debt-unified-data`

### 3. 数据格式要求

#### NewDebtBalanceChart 期望的数据格式：

```javascript
[
  {
    companyName: '公司名称',
    newDebtAmount: 1000, // 新增债权金额
    disposalAmount: 500, // 已处置金额
    remainingBalance: 500 // 剩余余额
  }
];
```

#### NewDebtCollectionChart 期望的数据格式：

```javascript
{
  monthNewAmount: 100,           // 本月新增
  cumulativeNewAmount: 1000,     // 累计新增
  monthDisposalAmount: 50,       // 本月处置
  cumulativeDisposalAmount: 500, // 累计处置
  periodEndBalance: 500          // 期末余额
}
```

## 后续建议

1. **后端数据一致性**：建议后端统一字段命名规范
2. **数据验证**：在前端添加更严格的数据格式验证
3. **错误处理**：增强错误提示，当数据获取失败时给用户明确提示
4. **性能优化**：考虑添加数据缓存，减少重复请求

## 测试验证

1. 刷新页面，查看图表标题是否正确显示
2. 检查控制台日志，确认数据获取成功
3. 验证图表数据是否正确显示
4. 切换不同的筛选条件，确认数据更新正常
