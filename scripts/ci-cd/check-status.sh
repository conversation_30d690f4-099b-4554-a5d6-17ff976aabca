#!/bin/bash

# 检查CI/CD系统状态脚本
echo "🔍 检查CI/CD系统状态..."

LINUX_SERVER="admin@10.25.1.85"

echo "📋 本地检查:"
echo "1. Git分支状态:"
git branch --show-current
git status --porcelain

echo -e "\n2. Git远程仓库:"
git remote -v

echo -e "\n3. 本地CI/CD配置:"
if [ -f ".ci-cd-config" ]; then
    echo "✅ CI/CD配置文件存在"
    cat .ci-cd-config
else
    echo "❌ CI/CD配置文件不存在"
fi

echo -e "\n4. 本地Git Hooks:"
ls -la .git/hooks/ | grep -E "(post-receive|pre-push)"

echo -e "\n📡 Linux服务器检查:"
ssh $LINUX_SERVER << 'EOF'
    echo "1. Webhook服务状态:"
    systemctl status financial-webhook || echo "Webhook服务未运行"
    
    echo -e "\n2. Docker容器状态:"
    docker ps | grep financial || echo "没有运行的financial容器"
    
    echo -e "\n3. 部署目录:"
    ls -la /home/<USER>/下载/FinancialSystem-Production-Deploy/ || echo "部署目录不存在"
    
    echo -e "\n4. Git仓库状态:"
    if [ -d "/opt/FinancialSystem/FinancialSystem.git" ]; then
        echo "✅ Git仓库存在"
        cd /opt/FinancialSystem/FinancialSystem.git
        git log -1 --oneline
    else
        echo "❌ Git仓库不存在"
    fi
    
    echo -e "\n5. 网络端口:"
    netstat -tlnp | grep -E "(8080|9000)" || echo "端口8080和9000未监听"
EOF

echo -e "\n💡 建议的修复步骤:"
echo "1. 运行 chmod +x fix-ssh-auth.sh && ./fix-ssh-auth.sh 修复SSH认证"
echo "2. 运行 chmod +x manual-deploy-to-linux.sh && ./manual-deploy-to-linux.sh 手动部署"
echo "3. 检查Linux服务器上的webhook服务: ssh $LINUX_SERVER 'sudo systemctl start financial-webhook'"
